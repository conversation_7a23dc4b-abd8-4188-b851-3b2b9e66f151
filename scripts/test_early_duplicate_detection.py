#!/usr/bin/env python3
"""
Test script for early duplicate detection functionality

This script tests the early duplicate detection feature by:
1. Creating mock LinkedIn URLs
2. Testing the PHP duplicate checker script
3. Verifying the filtering logic works correctly

Usage: python scripts/test_early_duplicate_detection.py
"""

import json
import tempfile
import subprocess
import os
import sys

def test_php_duplicate_checker():
    """Test the PHP duplicate checker script"""
    print("🧪 Testing PHP duplicate checker script...")
    
    # Create test data
    test_urls = [
        "https://linkedin.com/in/john-doe",
        "https://linkedin.com/in/jane-smith", 
        "https://linkedin.com/in/bob-johnson",
        "https://linkedin.com/in/alice-brown",
        "https://linkedin.com/in/charlie-wilson"
    ]
    
    test_data = {
        "linkedin_urls": test_urls,
        "plan_id": "test-plan-001"
    }
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
        json.dump(test_data, temp_file)
        temp_file_path = temp_file.name
    
    try:
        # Get script path
        script_dir = os.path.dirname(os.path.abspath(__file__))
        php_script_path = os.path.join(script_dir, '..', 'app', 'Scripts', 'check_duplicate_linkedin.php')
        
        print(f"📁 PHP script path: {php_script_path}")
        print(f"📄 Temp file path: {temp_file_path}")
        print(f"🔗 Testing {len(test_urls)} LinkedIn URLs")
        
        # Execute PHP script
        result = subprocess.run([
            'php', php_script_path, temp_file_path
        ], capture_output=True, text=True, timeout=30)
        
        print(f"📤 Return code: {result.returncode}")
        print(f"📤 STDOUT: {result.stdout}")
        
        if result.stderr:
            print(f"⚠️ STDERR: {result.stderr}")
        
        if result.returncode == 0:
            try:
                response_data = json.loads(result.stdout)
                print("✅ PHP script executed successfully!")
                print(f"📊 Results:")
                print(f"   • Total checked: {response_data.get('total_checked', 0)}")
                print(f"   • Duplicates found: {response_data.get('duplicates_found', 0)}")
                print(f"   • Processing time: {response_data.get('processing_time_ms', 0)}ms")
                print(f"   • Existing URLs: {response_data.get('existing_urls', [])}")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse JSON response: {e}")
                return False
        else:
            print(f"❌ PHP script failed with return code {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ PHP script timed out")
        return False
    except Exception as e:
        print(f"❌ Error executing PHP script: {e}")
        return False
    finally:
        # Clean up temp file
        try:
            os.unlink(temp_file_path)
        except:
            pass

def test_python_filtering_logic():
    """Test the Python filtering logic (mock)"""
    print("\n🧪 Testing Python filtering logic...")
    
    # Mock search results
    class MockResult:
        def __init__(self, url):
            self.url = url
    
    mock_results = [
        MockResult("https://linkedin.com/in/test-user-1"),
        MockResult("https://linkedin.com/in/test-user-2"),
        MockResult("https://linkedin.com/in/test-user-3"),
        MockResult("https://linkedin.com/in/test-user-4"),
        MockResult("https://linkedin.com/in/test-user-5"),
    ]
    
    print(f"📊 Mock search results: {len(mock_results)} profiles")
    
    # Extract URLs
    linkedin_urls = []
    for result in mock_results:
        url = getattr(result, 'url', None)
        if url and 'linkedin.com' in url:
            linkedin_urls.append(url)
    
    print(f"🔗 LinkedIn URLs extracted: {len(linkedin_urls)}")
    for url in linkedin_urls:
        print(f"   • {url}")
    
    # Simulate filtering (would normally call PHP script)
    print("✅ Python filtering logic test completed")
    return True

def main():
    """Main test function"""
    print("🚀 Starting Early Duplicate Detection Tests")
    print("=" * 50)
    
    # Test 1: PHP duplicate checker
    php_test_passed = test_php_duplicate_checker()
    
    # Test 2: Python filtering logic
    python_test_passed = test_python_filtering_logic()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    print(f"   • PHP duplicate checker: {'✅ PASSED' if php_test_passed else '❌ FAILED'}")
    print(f"   • Python filtering logic: {'✅ PASSED' if python_test_passed else '❌ FAILED'}")
    
    if php_test_passed and python_test_passed:
        print("\n🎉 All tests passed! Early duplicate detection is working correctly.")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
